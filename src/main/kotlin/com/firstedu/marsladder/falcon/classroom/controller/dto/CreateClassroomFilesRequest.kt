package com.firstedu.marsladder.falcon.classroom.controller.dto

import com.firstedu.marsladder.falcon.classroom.ClassroomFileState
import com.firstedu.marsladder.falcon.classroom.ClassroomFileType
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile
import com.firstedu.marsladder.falcon.utils.XssFilter
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.Size

data class CreateClassroomFilesRequest(
    @field:NotEmpty(message = "s3FileIds cannot be empty")
    val s3FileIds: List<String>,
    @field:Size(max = 255, message = "The maximum length is 255 letters")
    val name: String? = null, // 可选，如果不提供则使用文件原名
    @field:NotBlank(message = "currentFolderId cannot be null")
    val currentFolderId: String,
    val type: ClassroomFileType = ClassroomFileType.FILE, // 批量创建主要用于文件
) {
    fun xssFilter(
        xssFilter: XssFilter,
    ) = CreateClassroomFilesRequest(
        s3FileIds = s3FileIds,
        name = name?.let { xssFilter.filter(it) },
        currentFolderId = currentFolderId,
        type = type,
    )

    fun toClassroomFileTemplate(
        classroomId: String,
        uploadedBy: String,
    ) = ClassroomFile(
        name = name ?: "", // 实际名称会在service层根据S3文件确定
        parentId = currentFolderId,
        state = ClassroomFileState.UNPUBLISHED,
        type = type,
        deleted = false,
        rootFolder = false,
        uploadedBy = uploadedBy,
        classroomId = classroomId,
    )
}

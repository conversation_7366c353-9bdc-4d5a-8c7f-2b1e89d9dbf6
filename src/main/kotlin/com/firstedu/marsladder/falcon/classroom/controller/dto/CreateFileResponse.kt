package com.firstedu.marsladder.falcon.classroom.controller.dto

import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile

data class CreateFileResponse(
    val files: List<FileResponse>,
    val totalCount: Int,
) {
    companion object {
        fun fromSingle(classroomFile: ClassroomFile) = CreateFileResponse(
            files = listOf(FileResponse.from(classroomFile)),
            totalCount = 1,
        )
        
        fun fromMultiple(classroomFiles: List<ClassroomFile>) = CreateFileResponse(
            files = classroomFiles.map { FileResponse.from(it) },
            totalCount = classroomFiles.size,
        )
    }
}

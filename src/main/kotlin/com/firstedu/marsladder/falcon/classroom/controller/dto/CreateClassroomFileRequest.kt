package com.firstedu.marsladder.falcon.classroom.controller.dto

import com.firstedu.marsladder.falcon.classroom.ClassroomFileState
import com.firstedu.marsladder.falcon.classroom.ClassroomFileType
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile
import com.firstedu.marsladder.falcon.utils.XssFilter
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size

data class CreateClassroomFileRequest(
    val s3FileId: String? = null,
    val s3FileIds: List<String>? = null,
    @field:Size(max = 255, message = "The maximum length is 255 letters")
    val name: String,
    @field:NotBlank(message = "currentFolderId cannot be null")
    val currentFolderId: String,
    val type: ClassroomFileType,
) {
    init {
        // 验证：确保只提供其中一个字段，且至少提供一个（当type为FILE时）
        if (type == ClassroomFileType.FILE) {
            val hasS3FileId = s3FileId != null
            val hasS3FileIds = s3FileIds != null && s3FileIds.isNotEmpty()

            require(hasS3FileId || hasS3FileIds) {
                "Either s3FileId or s3FileIds must be provided for FILE type"
            }
            require(!(hasS3FileId && hasS3FileIds)) {
                "Cannot provide both s3FileId and s3FileIds"
            }
        }
    }

    fun xssFilter(
        xssFilter: XssFilter,
    ) = CreateClassroomFileRequest(
        s3FileId = s3FileId,
        s3FileIds = s3FileIds,
        name = xssFilter.filter(name),
        currentFolderId = currentFolderId,
        type = type,
    )

    fun getAllS3FileIds(): List<String> {
        return when {
            s3FileIds != null -> s3FileIds
            s3FileId != null -> listOf(s3FileId)
            else -> emptyList()
        }
    }

    fun toClassroomFile(
        classroomId: String,
        uploadedBy: String,
    ) = ClassroomFile(
        name = name,
        parentId = currentFolderId,
        state = ClassroomFileState.UNPUBLISHED,
        type = type,
        deleted = false,
        rootFolder = false,
        uploadedBy = uploadedBy,
        classroomId = classroomId,
    )
}

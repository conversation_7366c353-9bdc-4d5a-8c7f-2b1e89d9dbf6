package com.firstedu.marsladder.falcon.classroom.service.impl

import com.firstedu.marsladder.falcon.classroom.ClassroomFileState
import com.firstedu.marsladder.falcon.classroom.ClassroomFileType
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomFileRepository
import com.firstedu.marsladder.falcon.classroom.repository.entity.ClassroomFileEntity
import com.firstedu.marsladder.falcon.classroom.service.ClassroomFileService
import com.firstedu.marsladder.falcon.classroom.service.ClassroomService
import com.firstedu.marsladder.falcon.classroom.service.ClassroomTeacherService
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFolderStructure
import com.firstedu.marsladder.falcon.classroom.service.exception.ClassroomFileNotFoundException
import com.firstedu.marsladder.falcon.classroom.service.exception.InvalidClassroomFileException
import com.firstedu.marsladder.falcon.classroom.service.exception.InvalidClassroomFolderException
import com.firstedu.marsladder.falcon.config.S3Properties
import com.firstedu.marsladder.falcon.course.repository.CourseRepository
import com.firstedu.marsladder.falcon.file.repository.S3FileRepository
import com.firstedu.marsladder.falcon.file.repository.entity.S3FileEntity
import com.firstedu.marsladder.falcon.file.service.domain.S3File
import com.firstedu.marsladder.falcon.file.service.exception.S3FileNotFoundException
import com.firstedu.marsladder.falcon.file.service.exception.S3FileOperateErrorException
import com.firstedu.marsladder.falcon.infrastructure.aws.s3.ObjectClient
import com.firstedu.marsladder.falcon.utils.DateTimeUtil
import org.apache.commons.compress.utils.Lists
import org.apache.commons.io.FilenameUtils
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.util.UUID
import kotlin.jvm.optionals.getOrElse

@Service
class ClassroomFileServiceImpl(
    private val classroomFileRepository: ClassroomFileRepository,
    private val classroomService: ClassroomService,
    private val classroomTeacherService: ClassroomTeacherService,
    private val courseRepository: CourseRepository,
    private val objectClient: ObjectClient,
    private val s3Properties: S3Properties,
    private val s3FileRepository: S3FileRepository,
    private val dateTimeUtil: DateTimeUtil,
) : ClassroomFileService {
    private val log = LoggerFactory.getLogger(ClassroomFileServiceImpl::class.java)

    override fun getChildrenFilesByFolderId(
        classroomId: String,
        folderId: String,
    ): List<ClassroomFile> {
        classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, folderId, true)?.let {
            return Lists.newArrayList()
        }

        val classroomFileEntityList = classroomFileRepository.findByClassroomIdAndParentIdAndDeleted(classroomId, folderId, false)
        return classroomFileEntityList.map { classroomFileEntity ->
            val uploadedByName = classroomTeacherService.getClassroomTeacherOrTutorName(classroomFileEntity.classroomId, classroomFileEntity.uploadedBy)
            ClassroomFile.from(classroomFileEntity, uploadedByName)
        }
    }

    override fun getParentFolderPathByFolderId(
        classroomId: String,
        folderId: String,
    ): List<ClassroomFile> {
        val parentFolderPath = mutableListOf<ClassroomFile>()

        var currentFolderId: String? = folderId
        while (currentFolderId != null) {
            val classroomFileEntity = classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, currentFolderId, false)
            if (classroomFileEntity == null || classroomFileEntity.type != ClassroomFileType.FOLDER) {
                break
            }

            val uploadedByName = classroomTeacherService.getClassroomTeacherOrTutorName(classroomFileEntity.classroomId, classroomFileEntity.uploadedBy)
            parentFolderPath.add(ClassroomFile.from(classroomFileEntity, uploadedByName))

            currentFolderId = classroomFileEntity.parentId
        }

        parentFolderPath.reverse()
        return parentFolderPath
    }

    override fun getClassroomFileDetails(
        fileId: String,
    ): ClassroomFile {
        return classroomFileRepository.findByIdAndDeleted(fileId, false)
            ?.run {
                val uploadedByName = classroomTeacherService.getClassroomTeacherOrTutorName(classroomId, uploadedBy)
                ClassroomFile.from(this, uploadedByName)
            } ?: throw ClassroomFileNotFoundException("Classroom file not found, id: $fileId")
    }

    override fun initializeClassroomRootFolder(
        classroomId: String,
    ): ClassroomFile {
        val classroomRootFolder = getRootFolderByClassroom(classroomId)
        if (classroomRootFolder != null) {
            return classroomRootFolder
        }
        val courseEntity = courseRepository.findById(classroomService.getClassroomById(classroomId).courseId).getOrElse { null }
        val rootFolderName = if (courseEntity == null) "Files" else "Y${courseEntity.grade} ${courseEntity.name} Files"
        val classroomFileEntity =
            classroomFileRepository.save(
                ClassroomFileEntity(
                    name = rootFolderName,
                    type = ClassroomFileType.FOLDER,
                    state = ClassroomFileState.PUBLISHED,
                    rootFolder = true,
                    classroomId = classroomId,
                ),
            )
        return ClassroomFile.from(classroomFileEntity)
    }

    override fun updateClassroomFileName(
        classroomId: String,
        fileId: String,
        name: String,
    ) {
        classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, fileId, false)?.let {
            it.name =
                if (it.type == ClassroomFileType.FILE) {
                    getBaseFileName(name, it.s3FileEntity!!.extension)
                } else {
                    name
                }

            classroomFileRepository.save(it)
        } ?: throw ClassroomFileNotFoundException("Classroom file not found, id: $fileId")
    }

    override fun updateClassroomFileState(
        classroomId: String,
        fileId: String,
        state: ClassroomFileState,
    ) {
        val classroomFileEntity =
            classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, fileId, false)
                ?: throw ClassroomFileNotFoundException("Classroom file not found, id: $fileId")

        when (classroomFileEntity.type) {
            ClassroomFileType.FOLDER -> {
                if (state == ClassroomFileState.PUBLISHED) {
                    publishParentPathFolderState(classroomId, fileId)
                    changeChildrenFileState(classroomId, fileId, ClassroomFileState.PUBLISHED)
                } else {
                    updateChildrenPathFileState(classroomId, fileId, ClassroomFileState.UNPUBLISHED)
                }
            }

            ClassroomFileType.FILE -> {
                if (state == ClassroomFileState.PUBLISHED) {
                    publishParentPathFolderState(classroomId, fileId)
                } else {
                    updateSingleClassroomFileState(classroomId, fileId, ClassroomFileState.UNPUBLISHED)
                }
            }
        }
    }

    override fun deleteClassroomFile(
        classroomId: String,
        fileId: String,
    ) {
        classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, fileId, false)?.let {
            it.deleted = true
            classroomFileRepository.save(it)
        }
    }

    override fun creatClassroomFile(
        classroomFile: ClassroomFile,
        s3FileId: String?,
    ): ClassroomFile {
        val uploadedByName = classroomTeacherService.getClassroomTeacherOrTutorName(classroomFile.classroomId, classroomFile.uploadedBy)
        return when (classroomFile.type) {
            ClassroomFileType.FILE -> {
                val s3FileEntity =
                    s3FileRepository.findById(s3FileId!!)
                        .orElseThrow { S3FileNotFoundException("S3File not found, id: $s3FileId") }

                val fileName = getBaseFileName(classroomFile.name, s3FileEntity.extension)
                ClassroomFile.from(classroomFileRepository.save(classroomFile.toEntity(s3FileEntity, fileName)), uploadedByName)
            }

            else -> {
                ClassroomFile.from(classroomFileRepository.save(classroomFile.toEntity()), uploadedByName)
            }
        }
    }

    override fun createClassroomFiles(
        classroomFileTemplate: ClassroomFile,
        s3FileIds: List<String>,
    ): List<ClassroomFile> {
        if (s3FileIds.isEmpty()) {
            throw IllegalArgumentException("S3 file IDs cannot be empty")
        }

        val uploadedByName = classroomTeacherService.getClassroomTeacherOrTutorName(
            classroomFileTemplate.classroomId,
            classroomFileTemplate.uploadedBy
        )

        // 批量验证所有 S3 文件是否存在
        val s3FileEntities = s3FileIds.map { s3FileId ->
            s3FileRepository.findById(s3FileId)
                .orElseThrow { S3FileNotFoundException("S3File not found, id: $s3FileId") }
        }

        // 批量创建文件
        return s3FileEntities.map { s3FileEntity ->
            // 使用S3文件的原始文件名（去掉路径，保留扩展名）
            val originalFileName = s3FileEntity.objectKey.substringAfterLast("/")
            val fileName = getBaseFileName(originalFileName, s3FileEntity.extension)

            val fileToCreate = classroomFileTemplate.copy(name = fileName)
            ClassroomFile.from(
                classroomFileRepository.save(fileToCreate.toEntity(s3FileEntity, fileName)),
                uploadedByName
            )
        }
    }

    override fun uploadClassroomFile(
        classroomId: String,
        file: MultipartFile,
    ): S3File {
        val s3FileId = UUID.randomUUID().toString()
        val objectKey = "$classroomId/files/$s3FileId"

        val fileExtension = FilenameUtils.getExtension(file.originalFilename)
        val fileContentType = s3Properties.classroomFile.allowContentType[fileExtension.uppercase()]!!

        try {
            objectClient.putObject(
                s3Properties.classroomFile.bucket,
                objectKey,
                file,
                fileContentType,
            )
        } catch (e: Exception) {
            log.error("put file s3 object fail, objectKey:{}", objectKey, e)
            throw S3FileOperateErrorException("put file s3 object fail, objectKey: $objectKey")
        }
        val savedEntity =
            s3FileRepository.save(
                S3FileEntity(
                    s3FileId,
                    fileExtension,
                    s3Properties.classroomFile.bucket,
                    objectKey,
                ),
            )
        return S3File.from(savedEntity)
    }

    override fun getFolderStructure(
        classroomId: String,
        currentFileId: String?,
    ): ClassroomFolderStructure {
        val classroomRootFolder = getRootFolderByClassroom(classroomId) ?: throw ClassroomFileNotFoundException("Classroom $classroomId root folder not found.")
        val currentFileEntity = currentFileId?.let { classroomFileRepository.findById(it) }?.getOrElse { null }
        return buildFolderTree(classroomRootFolder, currentFileEntity)
    }

    private fun buildFolderTree(
        folder: ClassroomFile,
        currentFileEntity: ClassroomFileEntity?,
    ): ClassroomFolderStructure {
        val children =
            classroomFileRepository.findByParentIdAndTypeAndDeleted(folder.id!!, ClassroomFileType.FOLDER, false)
                .map { buildFolderTree(ClassroomFile.from(it), currentFileEntity) }

        return ClassroomFolderStructure(
            id = folder.id,
            name = folder.name,
            isCurrentFolder = if (currentFileEntity != null) currentFileEntity.parentId == folder.id else null,
            childrenFolders = children.ifEmpty { emptyList() },
        )
    }

    override fun moveClassroomFile(
        classroomId: String,
        sourceFileId: String,
        targetFolderId: String,
    ) {
        classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, targetFolderId, false)?.let {
            if (it.type == ClassroomFileType.FILE) throw InvalidClassroomFolderException("the move target cannot be a file，id：$targetFolderId")
        } ?: throw ClassroomFileNotFoundException("Classroom file not found, id: $targetFolderId")

        classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, sourceFileId, false)?.let {
            when {
                it.type == ClassroomFileType.FOLDER -> throw InvalidClassroomFileException("folder cannot be moved, id: $sourceFileId")
                else -> {
                    it.parentId = targetFolderId
                    classroomFileRepository.save(it)

                    if (it.state == ClassroomFileState.PUBLISHED) {
                        publishParentPathFolderState(classroomId, targetFolderId)
                    }
                }
            }
        } ?: throw InvalidClassroomFolderException("Classroom file not found, id: $sourceFileId")
    }

    private fun getRootFolderByClassroom(classroomId: String): ClassroomFile? {
        val classroomFiles = classroomFileRepository.findByClassroomIdAndRootFolder(classroomId, true)
        return classroomFiles.firstOrNull()?.let { ClassroomFile.from(it) }
    }

    private fun getBaseFileName(
        originFileName: String,
        fileExtension: String,
    ): String {
        val originFileExtension = FilenameUtils.getExtension(originFileName)
        return if (originFileExtension.equals(fileExtension, ignoreCase = true)) {
            FilenameUtils.removeExtension(originFileName)
        } else {
            originFileName
        }
    }

    private fun publishParentPathFolderState(
        classroomId: String,
        fileId: String,
    ) {
        var currentFileId: String? = fileId
        while (currentFileId != null) {
            val classroomFileEntity = updateSingleClassroomFileState(classroomId, currentFileId, ClassroomFileState.PUBLISHED)
            currentFileId = classroomFileEntity.parentId
        }
    }

    private fun updateChildrenPathFileState(
        classroomId: String,
        fileId: String,
        state: ClassroomFileState,
    ) {
        updateSingleClassroomFileState(classroomId, fileId, state)
        changeChildrenFileState(classroomId, fileId, state)
    }

    private fun changeChildrenFileState(
        classroomId: String,
        parentId: String,
        state: ClassroomFileState,
    ) {
        val childrenList = classroomFileRepository.findByClassroomIdAndParentIdAndDeleted(classroomId, parentId, false)

        childrenList.forEach {
            it.publishedAt = dateTimeUtil.getNowTime()
            it.state = state
            classroomFileRepository.save(it)

            if (it.type == ClassroomFileType.FOLDER) {
                changeChildrenFileState(classroomId, it.id!!, state)
            }
        }
    }

    private fun updateSingleClassroomFileState(
        classroomId: String,
        fileId: String,
        state: ClassroomFileState,
    ): ClassroomFileEntity {
        val entity =
            classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, fileId, false)
                ?: throw ClassroomFileNotFoundException("Classroom file not found, id: $fileId")
        return classroomFileRepository.save(entity.copy(publishedAt = dateTimeUtil.getNowTime(), state = state))
    }
}

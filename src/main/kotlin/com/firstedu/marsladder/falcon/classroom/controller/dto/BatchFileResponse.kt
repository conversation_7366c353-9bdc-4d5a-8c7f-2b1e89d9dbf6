package com.firstedu.marsladder.falcon.classroom.controller.dto

import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile

data class BatchFileResponse(
    val files: List<FileResponse>,
    val totalCount: Int,
    val successCount: Int,
    val failedCount: Int,
) {
    companion object {
        fun from(classroomFiles: List<ClassroomFile>) = BatchFileResponse(
            files = classroomFiles.map { FileResponse.from(it) },
            totalCount = classroomFiles.size,
            successCount = classroomFiles.size,
            failedCount = 0,
        )
    }
}

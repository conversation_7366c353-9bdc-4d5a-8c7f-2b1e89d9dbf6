package com.firstedu.marsladder.falcon.classroom.controller

import com.firstedu.marsladder.falcon.classroom.ClassroomFileState
import com.firstedu.marsladder.falcon.classroom.controller.dto.ClassroomFileResponse
import com.firstedu.marsladder.falcon.classroom.controller.dto.ClassroomFileStateResponse
import com.firstedu.marsladder.falcon.classroom.controller.dto.ClassroomFolderStructureResponse
import com.firstedu.marsladder.falcon.classroom.controller.dto.CreateClassroomFileRequest
import com.firstedu.marsladder.falcon.classroom.controller.dto.CreateFileResponse
import com.firstedu.marsladder.falcon.classroom.controller.dto.FileResponse
import com.firstedu.marsladder.falcon.classroom.controller.dto.UpdateClassroomFileNameRequest
import com.firstedu.marsladder.falcon.classroom.controller.dto.UpdateClassroomFileStateRequest
import com.firstedu.marsladder.falcon.classroom.controller.dto.filterXss
import com.firstedu.marsladder.falcon.classroom.service.ClassroomFileService
import com.firstedu.marsladder.falcon.classroom.service.ClassroomService
import com.firstedu.marsladder.falcon.config.S3Properties
import com.firstedu.marsladder.falcon.exception.NoPermissionException
import com.firstedu.marsladder.falcon.file.controller.dto.S3FileResponse
import com.firstedu.marsladder.falcon.file.service.exception.InvalidFileException
import com.firstedu.marsladder.falcon.security.SessionProvider
import com.firstedu.marsladder.falcon.utils.XssFilter
import org.apache.commons.io.FilenameUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

@PreAuthorize("hasAnyRole('TEACHER','TUTOR')")
@RestController
class ClassroomFileController(
    private val sessionProvider: SessionProvider,
    private val classroomService: ClassroomService,
    private val classroomFileService: ClassroomFileService,
    private val s3Properties: S3Properties,
    private val xssFilter: XssFilter,
) {
    @GetMapping("/classrooms/{classroomId}/files/{folderId}")
    fun getClassroomItems(
        @PathVariable classroomId: String,
        @PathVariable folderId: String,
    ): ResponseEntity<ClassroomFileResponse> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId())
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")

        return ResponseEntity.ok(
            ClassroomFileResponse.from(
                classroomFileService.getParentFolderPathByFolderId(classroomId, folderId),
                classroomFileService.getChildrenFilesByFolderId(classroomId, folderId),
            ),
        )
    }

    @GetMapping("/classroom-files/{fileId}")
    @PreAuthorize("hasAnyRole('TEACHER','TUTOR','CONSUMER')")
    fun getClassroomFile(
        @PathVariable fileId: String,
    ): ResponseEntity<FileResponse> {
        return ResponseEntity.ok(
            FileResponse.from(classroomFileService.getClassroomFileDetails(fileId)),
        )
    }

    @GetMapping("classroom-file-states")
    fun getClassroomFileStates(): ResponseEntity<List<ClassroomFileStateResponse>> {
        return ResponseEntity.ok(
            ClassroomFileState.entries.map { state ->
                ClassroomFileStateResponse(
                    value = state.name,
                    label = state.displayName,
                )
            },
        )
    }

    @PatchMapping("/classrooms/{classroomId}/files/{fileId}/state")
    fun updateClassroomFileState(
        @PathVariable classroomId: String,
        @PathVariable fileId: String,
        @RequestBody requestBody: UpdateClassroomFileStateRequest,
    ): ResponseEntity<Unit> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId())
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")
        classroomFileService.updateClassroomFileState(classroomId, fileId, requestBody.state)
        return ResponseEntity.ok().build()
    }

    @PatchMapping("/classrooms/{classroomId}/files/{fileId}/name")
    fun updateClassroomFileName(
        @PathVariable classroomId: String,
        @PathVariable fileId: String,
        @RequestBody requestBody: UpdateClassroomFileNameRequest,
    ): ResponseEntity<Unit> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId())
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")
        classroomFileService.updateClassroomFileName(classroomId, fileId, requestBody.filterXss(xssFilter).name)
        return ResponseEntity.ok().build()
    }

    @DeleteMapping("/classrooms/{classroomId}/files/{fileId}")
    fun deleteClassroomFile(
        @PathVariable classroomId: String,
        @PathVariable fileId: String,
    ): ResponseEntity<Unit> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId())
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")
        classroomFileService.deleteClassroomFile(classroomId, fileId)
        return ResponseEntity.ok().build()
    }

    @PostMapping("/classrooms/{classroomId}/files")
    fun createClassroomFile(
        @PathVariable classroomId: String,
        @RequestBody createClassroomFileRequest: CreateClassroomFileRequest,
    ): ResponseEntity<CreateFileResponse> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId())
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")

        val filteredRequest = createClassroomFileRequest.xssFilter(xssFilter)
        val s3FileIds = filteredRequest.getAllS3FileIds()

        val classroomFiles = classroomFileService.createClassroomFiles(
            filteredRequest.toClassroomFile(classroomId, sessionProvider.getUserId()),
            s3FileIds,
        )

        val response = CreateFileResponse.fromMultiple(classroomFiles)
        return ResponseEntity.status(HttpStatus.CREATED).body(response)
    }

    @PostMapping("/classrooms/{classroomId}/s3-files/upload")
    fun uploadClassroomS3File(
        @PathVariable classroomId: String,
        @RequestParam file: MultipartFile,
    ): ResponseEntity<S3FileResponse> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId())
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")

        validateClassroomFile(file)
        val s3File = classroomFileService.uploadClassroomFile(classroomId, file)
        return ResponseEntity.status(HttpStatus.CREATED).body(S3FileResponse.from(s3File))
    }

    @GetMapping("/classrooms/{classroomId}/folder-structure")
    fun getClassroomFolderStructure(
        @PathVariable classroomId: String,
        @RequestParam(required = false) currentFileId: String? = null,
    ): ResponseEntity<ClassroomFolderStructureResponse> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId())
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")
        return ResponseEntity.ok(
            ClassroomFolderStructureResponse.from
                (classroomFileService.getFolderStructure(classroomId, currentFileId)),
        )
    }

    @PatchMapping("/classrooms/{classroomId}/files/{fileId}/move")
    fun moveClassroomS3File(
        @PathVariable classroomId: String,
        @PathVariable fileId: String,
        @RequestParam targetFolderId: String,
    ): ResponseEntity<Unit> {
        classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, sessionProvider.getUserId())
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")
        classroomFileService.moveClassroomFile(classroomId, fileId, targetFolderId)
        return ResponseEntity.ok().build()
    }

    private fun validateClassroomFile(
        file: MultipartFile,
    ) {
        val fileExtension = FilenameUtils.getExtension(file.originalFilename)

        if (StringUtils.isBlank(fileExtension)) {
            throw InvalidFileException("Invalid assignment extension is null")
        }

        if (!s3Properties.classroomFile.allowContentType.containsKey(fileExtension.uppercase())) {
            throw InvalidFileException("Invalid Classroom file extension $fileExtension")
        }
        if (file.size > s3Properties.classroomFile.maxFileSize) {
            throw InvalidFileException("File too large.")
        }
    }
}

package com.firstedu.marsladder.falcon.classroom.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.firstedu.marsladder.falcon.classroom.ClassroomFileType
import com.firstedu.marsladder.falcon.classroom.controller.dto.CreateClassroomFileRequest
import com.firstedu.marsladder.falcon.classroom.service.ClassroomFileService
import com.firstedu.marsladder.falcon.classroom.service.ClassroomService
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile
import com.firstedu.marsladder.falcon.config.S3Properties
import com.firstedu.marsladder.falcon.file.service.domain.S3File
import com.firstedu.marsladder.falcon.security.SessionProvider
import com.firstedu.marsladder.falcon.utils.XssFilter
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@WebMvcTest(ClassroomFileController::class)
internal class ClassroomFileControllerBatchTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockBean
    private lateinit var classroomFileService: ClassroomFileService

    @MockBean
    private lateinit var classroomService: ClassroomService

    @MockBean
    private lateinit var sessionProvider: SessionProvider

    @MockBean
    private lateinit var s3Properties: S3Properties

    @MockBean
    private lateinit var xssFilter: XssFilter

    private val classroomId = "640ce7fb-f25a-41f5-8e68-f83d7e573a95"
    private val userId = "de2192de-9b01-11ed-988c-0aee35cf62c8"

    @Test
    @WithMockUser(username = userId, roles = ["TEACHER"])
    fun `should create single file and return FileResponse when single s3FileId provided`() {
        // Given
        val request = CreateClassroomFileRequest(
            s3FileId = "file1",
            s3FileIds = null,
            name = "test.pdf",
            currentFolderId = "folder1",
            type = ClassroomFileType.FILE
        )

        val mockClassroomFile = ClassroomFile(
            id = "created-file-id",
            s3File = S3File("file1", "pdf", "bucket", "key"),
            name = "test.pdf",
            parentId = "folder1",
            state = com.firstedu.marsladder.falcon.classroom.ClassroomFileState.UNPUBLISHED,
            type = ClassroomFileType.FILE,
            classroomId = classroomId
        )

        whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, userId)).thenReturn(true)
        whenever(sessionProvider.getUserId()).thenReturn(userId)
        whenever(xssFilter.filter(any<String>())).thenAnswer { it.arguments[0] }
        whenever(classroomFileService.createClassroomFiles(any(), any())).thenReturn(listOf(mockClassroomFile))

        // When & Then
        mockMvc.perform(
            post("/classrooms/$classroomId/files")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isCreated)
            .andExpect(jsonPath("$.id").value("created-file-id"))
            .andExpect(jsonPath("$.name").value("test.pdf"))
            .andExpect(jsonPath("$.s3File.id").value("file1"))
    }

    @Test
    @WithMockUser(username = userId, roles = ["TEACHER"])
    fun `should create multiple files and return BatchFileResponse when multiple s3FileIds provided`() {
        // Given
        val request = CreateClassroomFileRequest(
            s3FileId = null,
            s3FileIds = listOf("file1", "file2"),
            name = "batch files",
            currentFolderId = "folder1",
            type = ClassroomFileType.FILE
        )

        val mockClassroomFiles = listOf(
            ClassroomFile(
                id = "created-file-id-1",
                s3File = S3File("file1", "pdf", "bucket", "key1"),
                name = "file1.pdf",
                parentId = "folder1",
                state = com.firstedu.marsladder.falcon.classroom.ClassroomFileState.UNPUBLISHED,
                type = ClassroomFileType.FILE,
                classroomId = classroomId
            ),
            ClassroomFile(
                id = "created-file-id-2",
                s3File = S3File("file2", "jpg", "bucket", "key2"),
                name = "file2.jpg",
                parentId = "folder1",
                state = com.firstedu.marsladder.falcon.classroom.ClassroomFileState.UNPUBLISHED,
                type = ClassroomFileType.FILE,
                classroomId = classroomId
            )
        )

        whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, userId)).thenReturn(true)
        whenever(sessionProvider.getUserId()).thenReturn(userId)
        whenever(xssFilter.filter(any<String>())).thenAnswer { it.arguments[0] }
        whenever(classroomFileService.createClassroomFiles(any(), any())).thenReturn(mockClassroomFiles)

        // When & Then
        mockMvc.perform(
            post("/classrooms/$classroomId/files")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isCreated)
            .andExpect(jsonPath("$.totalCount").value(2))
            .andExpect(jsonPath("$.files[0].id").value("created-file-id-1"))
            .andExpect(jsonPath("$.files[0].name").value("file1.pdf"))
            .andExpect(jsonPath("$.files[1].id").value("created-file-id-2"))
            .andExpect(jsonPath("$.files[1].name").value("file2.jpg"))
    }
}

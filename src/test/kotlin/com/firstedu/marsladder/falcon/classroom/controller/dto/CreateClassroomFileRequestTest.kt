package com.firstedu.marsladder.falcon.classroom.controller.dto

import com.firstedu.marsladder.falcon.classroom.ClassroomFileType
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.Assertions.assertEquals

internal class CreateClassroomFileRequestTest {

    @Test
    fun `should return single s3FileId when only s3FileId is provided`() {
        val request = CreateClassroomFileRequest(
            s3FileId = "file1",
            s3FileIds = null,
            name = "test",
            currentFolderId = "folder1",
            type = ClassroomFileType.FILE
        )
        
        assertEquals(listOf("file1"), request.getAllS3FileIds())
    }

    @Test
    fun `should return multiple s3FileIds when s3FileIds is provided`() {
        val request = CreateClassroomFileRequest(
            s3FileId = null,
            s3FileIds = listOf("file1", "file2", "file3"),
            name = "test",
            currentFolderId = "folder1",
            type = ClassroomFileType.FILE
        )
        
        assertEquals(listOf("file1", "file2", "file3"), request.getAllS3FileIds())
    }

    @Test
    fun `should return empty list when neither s3FileId nor s3FileIds is provided for FOLDER type`() {
        val request = CreateClassroomFileRequest(
            s3FileId = null,
            s3FileIds = null,
            name = "test",
            currentFolderId = "folder1",
            type = ClassroomFileType.FOLDER
        )
        
        assertEquals(emptyList(), request.getAllS3FileIds())
    }

    @Test
    fun `should throw exception when both s3FileId and s3FileIds are provided for FILE type`() {
        assertThrows<IllegalArgumentException> {
            CreateClassroomFileRequest(
                s3FileId = "file1",
                s3FileIds = listOf("file2", "file3"),
                name = "test",
                currentFolderId = "folder1",
                type = ClassroomFileType.FILE
            )
        }
    }

    @Test
    fun `should throw exception when neither s3FileId nor s3FileIds is provided for FILE type`() {
        assertThrows<IllegalArgumentException> {
            CreateClassroomFileRequest(
                s3FileId = null,
                s3FileIds = null,
                name = "test",
                currentFolderId = "folder1",
                type = ClassroomFileType.FILE
            )
        }
    }

    @Test
    fun `should throw exception when s3FileIds is empty for FILE type`() {
        assertThrows<IllegalArgumentException> {
            CreateClassroomFileRequest(
                s3FileId = null,
                s3FileIds = emptyList(),
                name = "test",
                currentFolderId = "folder1",
                type = ClassroomFileType.FILE
            )
        }
    }
}
